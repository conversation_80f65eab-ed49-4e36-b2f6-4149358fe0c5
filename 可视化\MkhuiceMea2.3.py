# MkhuiceMea2.2.py (Refactored Version with Cooldown Feature)

import time
import pandas as pd
import numpy as np
from typing import List, Tuple, Dict, Any
import warnings
import matplotlib.pyplot as plt
import mplfinance as mpf
import matplotlib.patches as mpatches
from matplotlib.font_manager import FontProperties, findfont, FontManager

# Suppress specific warnings for a cleaner output
warnings.filterwarnings('ignore', category=UserWarning)
warnings.filterwarnings('ignore', category=DeprecationWarning)

# --- Import Custom Modules ---
# It's assumed these modules are in the same directory or Python path.
from okx_sdkV1 import get_kline_data, OKXClient
from MkKu import save_json, save_df_to_excel, get_local_kline_data, analyze_trend_changes, get_trade_decision_v2, calculate_trading_profit, plot_capital_curve_v1
from MKTrade import rolling_trade, analyze_trading_performance, plot_trading_results
# 简化版本：删除了复杂的json订单管理和动态止盈止损模块

# --- Visualization & Font Management ---

def setup_chinese_fonts():
    """
    Finds and sets a suitable Chinese font for matplotlib plots.
    """
    font_candidates = [
        'SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'WenQuanYi Micro Hei', 
        'PingFang SC', 'Noto Sans CJK SC', 'Source Han Sans SC'
    ]
    for font in font_candidates:
        if findfont(FontProperties(family=font)):
            plt.rcParams['font.sans-serif'] = [font]
            print(f"中文字体已设置为: {font}")
            break
    else:
        print("警告: 未找到指定的中文字体，图形可能无法正确显示中文。")
    plt.rcParams['axes.unicode_minus'] = False

def plot_kline_with_trade_signals(df: pd.DataFrame, title: str = 'K线图与交易信号', figsize: tuple = (18, 10)):
    """
    绘制K线图，并在图上标记买入和卖出信号。

    Args:
        df (pd.DataFrame): 包含 OHLC 和交易信号 ('tradeS') 的数据。
        title (str): 图表标题。
        figsize (tuple): 图表尺寸。
    """
    if df.empty or 'tradeS' not in df.columns:
        raise ValueError("DataFrame 必须非空且包含 'tradeS' 列。")

    df_plot = df.copy()
    df_plot.rename(columns={'open': 'Open', 'high': 'High', 'low': 'Low', 'close': 'Close'}, inplace=True)
    df_plot.index = pd.to_datetime(df_plot['open_time'])

    # 创建买卖信号的散点图
    addplots = []
    long_signals = df_plot['tradeS'] == 1
    short_signals = df_plot['tradeS'] == -1
    
    if long_signals.any():
        buy_markers = np.where(long_signals, df_plot['Low'] * 0.998, np.nan)
        addplots.append(mpf.make_addplot(buy_markers, type='scatter', marker='^', color='green', markersize=100))
        
    if short_signals.any():
        sell_markers = np.where(short_signals, df_plot['High'] * 1.002, np.nan)
        addplots.append(mpf.make_addplot(sell_markers, type='scatter', marker='v', color='red', markersize=100))

    # 设置图表样式并绘制
    mc = mpf.make_marketcolors(up='g', down='r', inherit=True)
    s = mpf.make_mpf_style(marketcolors=mc, gridstyle=':')
    
    fig, axes = mpf.plot(df_plot, type='candle', style=s, addplot=addplots,
                         figsize=figsize, returnfig=True, show_nontrading=False,
                         title=title, ylabel='价格')
    
    # 创建图例
    legend_elements = [
        plt.Line2D([0], [0], marker='^', color='w', markerfacecolor='green', markersize=10, label='做多信号 (1)', linestyle='None'),
        plt.Line2D([0], [0], marker='v', color='w', markerfacecolor='red', markersize=10, label='做空信号 (-1)', linestyle='None')
    ]
    axes[0].legend(handles=legend_elements, loc='upper left')

    # 在图上添加统计信息
    stats_text = (f"交易信号统计:\n"
                  f"做多: {long_signals.sum()}次\n"
                  f"做空: {short_signals.sum()}次\n"
                  f"总计: {long_signals.sum() + short_signals.sum()}次")
    axes[0].text(0.02, 0.98, stats_text, transform=axes[0].transAxes, fontsize=9,
                 verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    plt.tight_layout()
    mpf.show()

# --- Core Trading Logic & Analysis Functions ---

def calculate_ema(data: pd.Series, period: int) -> pd.Series:
    """
    计算指数移动平均值 (EMA)。
    如果数据长度不足，返回一个填充了 NaN 的序列。
    """
    if len(data) < period:
        return pd.Series(np.nan, index=data.index)
    return data.ewm(span=period, adjust=False).mean()

def calculate_ema_optimized(df: pd.DataFrame, column: str, period: int, N: int = 3) -> pd.Series:
    """
    通过对数据进行切片来优化EMA计算，避免在长序列上进行不必要的计算。
    """
    threshold = N * period
    if len(df) > threshold:
        slice_df = df.iloc[-threshold:]
    else:
        slice_df = df
    
    ema_result = calculate_ema(slice_df[column], period)
    
    # 将计算结果对齐到原始DataFrame的索引
    return ema_result.reindex(df.index)

def analyze_ema_state(ema_values: list, tolerance_percent: float = 0.005) -> str:
    """
    根据短、中、长三个EMA值的关系，分析当前市场状态（九宫格状态）。
    """
    if len(ema_values) != 3 or any(pd.isna(v) for v in ema_values):
        return "0. 数据不足/无法计算"

    a, b, c = ema_values
    
    # 判定 A 和 B 的关系
    ab_state = "approx" if abs(a - b) / max(abs(b), 1e-9) <= tolerance_percent else ("greater" if a > b else "less")
    # 判定 B 和 C 的关系
    bc_state = "approx" if abs(b - c) / max(abs(c), 1e-9) <= tolerance_percent else ("greater" if b > c else "less")

    # 根据关系组合匹配结果
    if ab_state == "greater":
        if bc_state == "greater": return "1. 强势多头 (A > B > C)"
        if bc_state == "less": return "2. 下跌中反弹"
        return "3. 盘整后突破" # bc_state == "approx"
    elif ab_state == "less":
        if bc_state == "greater": return "4. 上涨中回调"
        if bc_state == "less": return "5. 强势空头 (C > B > A)"
        return "6. 盘整后破位" # bc_state == "approx"
    else: # ab_state == "approx"
        if bc_state == "greater": return "7. 上涨趋势减弱"
        if bc_state == "less": return "8. 下跌趋势减弱"
        return "9. 极限盘整/无趋势" # bc_state == "approx"

def analyze_kline_performance(df: pd.DataFrame, open_direction: int) -> tuple[list, pd.Series]:
    """
    分析开仓信号发出后，未来一段时间内的K线表现，计算最大潜在盈利和最大回撤。
    """
    if df.empty or len(df) < 2:
        return [open_direction, "N/A", "N/A"], pd.Series()

    initial_price = df.iloc[0]['open']
    if initial_price == 0:
        return [open_direction, "N/A", "N/A"], pd.Series()
    
    max_price_overall = df['high'].max()
    min_price_overall = df['low'].min()

    # 计算整体最大盈利 (max_good) 和最大回撤 (max_bad)
    if open_direction == 1: # 开多
        max_good = (max_price_overall / initial_price) - 1
        max_bad = (min_price_overall / initial_price) - 1
    else: # 开空
        max_good = (initial_price / min_price_overall) - 1 if min_price_overall != 0 else np.inf
        max_bad = (initial_price / max_price_overall) - 1 if max_price_overall != 0 else -np.inf

    # 计算每根K线的涨跌幅
    open_safe = df['open'].replace(0, np.nan)
    percentage_change = (df['close'] - df['open']) / open_safe
    
    # 格式化输出
    formatted_max_good = f"{max_good:.3%}" if np.isfinite(max_good) else str(max_good)
    formatted_max_bad = f"{max_bad:.3%}" if np.isfinite(max_bad) else str(max_bad)
    
    return [open_direction, formatted_max_good, formatted_max_bad], percentage_change

def calculate_final_trade_signal(slice_df, periods, tolerance_percent, price_columns, price_columns_reversed, 
                                 buy_nowSigle, sell_nowSigle):
    """
    整合EMA计算和状态分析，得出最终的买入或卖出信号。
    """
    # --- 计算买入信号 ---
    LivemaS_buy = [calculate_ema_optimized(slice_df, col, per).iloc[-1] for col, per in zip(price_columns, periods)]
    state_for_buy = analyze_ema_state(LivemaS_buy, tolerance_percent)
    new_buy_nowSigle = int(state_for_buy[0]) if state_for_buy[0].isdigit() else buy_nowSigle
    trade_signal_buy, reason_buy = get_trade_decision_v2(buy_nowSigle, new_buy_nowSigle, current_position='flat', trade_direction='both')

    # --- 计算卖出信号 ---
    LivemaS_sell = [calculate_ema_optimized(slice_df, col, per).iloc[-1] for col, per in zip(price_columns_reversed, periods)]
    state_for_sell = analyze_ema_state(LivemaS_sell, tolerance_percent)
    new_sell_nowSigle = int(state_for_sell[0]) if state_for_sell[0].isdigit() else sell_nowSigle
    trade_signal_sell, reason_sell = get_trade_decision_v2(sell_nowSigle, new_sell_nowSigle, current_position='flat', trade_direction='both')

    # --- 决策逻辑 ---
    trade_signal, trade_reason, trade_state = 0, "No Signal", "N/A"
    if trade_signal_buy == 'BUY':
        trade_signal, trade_reason, trade_state = 1, reason_buy, state_for_buy
        if trade_signal_sell == 'SHORT':
            print(f"第📊{slice_df.index[-1]}发生冲突：BUY {reason_buy} 和 SHORT {reason_sell}")
    elif trade_signal_sell == 'SHORT':
        trade_signal, trade_reason, trade_state = -1, reason_sell, state_for_sell
        
    return trade_signal, trade_reason, trade_state, new_buy_nowSigle, new_sell_nowSigle


def plot_cumulative_returns(trade_stats_df: pd.DataFrame):
    """根据交易统计绘制累计收益曲线。"""
    if trade_stats_df.empty:
        print("没有交易记录可供绘图。")
        return
        
    cumulative_returns = trade_stats_df['change_pct'].cumsum()
    
    plt.figure(figsize=(12, 6))
    plt.plot(cumulative_returns.index, cumulative_returns, 'b-', linewidth=2, label='Cumulative Returns')
    plt.title('交易涨跌幅累计曲线')
    plt.xlabel('交易次数')
    plt.ylabel('累计涨跌幅 (%)')
    plt.grid(True, which='both', linestyle='--', linewidth=0.5)
    plt.legend()
    plt.show()
    
    print(f"\n交易统计: 总交易次数={len(trade_stats_df)}, 累计收益={cumulative_returns.iloc[-1]:.2f}%")

# --- Main Execution Block ---

if __name__ == "__main__":
    
    # --- 1. 配置策略参数 ---
    config = {
        'Uname': 'doge',
        'Ktime': '1m',
        'strTime': '2025-4-1',
        'endTime': '2025-4-10',
        'count': 100,  # 滑动窗口大小
        'goodBads': 24, # 未来表现分析的K线数量
        'periods': [8, 16, 32], # EMA周期
        'tolerance_percent': 0.0003, # analyze_ema_state灵敏度
        'price_columns': ['low', 'close', 'high'],
        'leverage': 10,
        'take_profit_pct': 0.05,
        'stop_loss_pct': 0.01,
        'signal_cooldown_threshold': 2, # 新增：信号冷却阈值（K线数量）。例如设为5，则一个买入信号后，5根K线内的后续买入信号将被忽略。
    }
    config['price_columns_reversed'] = config['price_columns'][::-1]
    
    # 设置绘图字体
    setup_chinese_fonts()

    # --- 2. 加载数据 ---
    try:
        df = get_local_kline_data(config['Uname'], config['Ktime'], config['strTime'], config['endTime'])
        print(f"成功加载数据: {len(df)} 条K线")
        print(f"数据时间范围: {df['open_time'].iloc[0]} -> {df['open_time'].iloc[-1]}")
    except Exception as e:
        print(f"加载数据失败: {e}")
        exit()

    # --- 3. 初始化状态变量 ---
    state = {
        'buy_nowSigle': 0,
        'sell_nowSigle': 0,
        'chicangPosition': 0, # 0: 无仓位, 1: 持有多仓, -1: 持有空仓
        'last_signal_type': 0, # 上一个有效信号的类型 (1或-1)
        'bars_since_last_signal': 999, # 距离上个信号的K线计数，初始设为大数以允许第一个信号
        'last_open_price': 0.0, # 最后一次开仓价格
    }
    
    # 简化版本：删除了复杂的订单管理和动态策略
    
    results = [] # 用于存储每一步的结果
    trade_stats_records = [] # 用于存储每笔完整交易的统计

    # --- 4. 主回测循环 ---
    max_start_index = len(df) - config['count'] - config['goodBads']
    for start_idx in range(max_start_index + 1):
        end_idx = start_idx + config['count']
        slice_df = df.iloc[start_idx:end_idx]

        k_info = {
            'time': slice_df.iloc[-1]['open_time'],
            'high': slice_df.iloc[-1]['high'],
            'low': slice_df.iloc[-1]['low'],
            'close': slice_df.iloc[-1]['close']
        }

        # --- 计算交易信号 ---
        trade_signal, reason, ema_state, buy_sig, sell_sig = calculate_final_trade_signal(
            slice_df, config['periods'], config['tolerance_percent'], config['price_columns'],
            config['price_columns_reversed'], state['buy_nowSigle'], state['sell_nowSigle']
        )
        state['buy_nowSigle'], state['sell_nowSigle'] = buy_sig, sell_sig

        # 信号冷却功能
        state['bars_since_last_signal'] += 1
        if state['bars_since_last_signal'] <= config['signal_cooldown_threshold']:
            if trade_signal != 0 and trade_signal == state['last_signal_type']:
                print(f"--- 信号冷却: 忽略相同的 {('BUY' if trade_signal == 1 else 'SHORT')} 信号 ---")
                trade_signal = 0

        # --- 处理交易信号 ---
        if trade_signal != 0 and trade_signal != state['chicangPosition']:
            current_trade_price = k_info['close']

            # 如果有持仓，先平仓
            if state['chicangPosition'] != 0:
                huadianPrice = current_trade_price * (1 + 0.0005 * state['chicangPosition'])
                closed_pnl = ((huadianPrice / state['last_open_price']) - 1) if state['chicangPosition'] == 1 else ((state['last_open_price'] / huadianPrice) - 1)
                trade_stats_records.append({'direction': 'buy' if state['chicangPosition'] == 1 else 'sell', 'open_price': state['last_open_price'], 'close_price': huadianPrice, 'change_pct': closed_pnl * 100})
                print(f"反手平仓: {'BUY' if state['chicangPosition'] == 1 else 'SHORT'} 开仓价={state['last_open_price']:.5f} 平仓价={huadianPrice:.5f} 盈亏={closed_pnl:.2%}")

            # 开新仓
            open_price = current_trade_price * (1 - trade_signal * 0.0005)
            print(f"开仓: {'BUY' if trade_signal == 1 else 'SHORT'} 信号: {reason} 价格: {open_price:.5f}")

            # 更新持仓状态
            state['chicangPosition'] = trade_signal
            state['last_open_price'] = open_price
            state['last_signal_type'] = trade_signal
            state['bars_since_last_signal'] = 0

        # --- 记录当期结果 ---
        any_df = df.iloc[end_idx : end_idx + config['goodBads']]
        good_bad, _ = analyze_kline_performance(any_df, trade_signal) if trade_signal != 0 else ([0, 0, 0], None)

        results.append({
            'index': end_idx,
            'tradeS': trade_signal,
            'tradePrice': k_info['close'] if trade_signal != 0 else 0,
            'emaStas': ema_state,
            'any_good_bad': good_bad
        })
        
    # --- 5. 回测结束后处理未平仓 ---
    if state['chicangPosition'] != 0:
        final_close_price = df.iloc[-1]['close']
        final_pnl = ((final_close_price / state['last_open_price']) - 1) if state['chicangPosition'] == 1 else ((state['last_open_price'] / final_close_price) - 1)
        trade_stats_records.append({
            'direction': 'buy' if state['chicangPosition'] == 1 else 'sell',
            'open_price': state['last_open_price'],
            'close_price': final_close_price,
            'change_pct': final_pnl * 100
        })
        print(f"回测结束强制平仓: {'BUY' if state['chicangPosition'] == 1 else 'SHORT'} 开仓价={state['last_open_price']:.5f} 平仓价={final_close_price:.5f} 盈亏={final_pnl:.2%}")
        state['chicangPosition'] = 0

    # --- 6. 后处理与可视化 ---
    trade_stats_df = pd.DataFrame(trade_stats_records)
    if results:
        results_df = pd.DataFrame(results).set_index('index')
        df = df.join(results_df)
        df.fillna(0, inplace=True)

    # save_df_to_excel(df,'final_results_with_cooldown')

    plot_cumulative_returns(trade_stats_df)
    plot_kline_with_trade_signals(df, title=f"{config['Uname']}/{config['Ktime']} 交易回测分析 (含信号冷却)")